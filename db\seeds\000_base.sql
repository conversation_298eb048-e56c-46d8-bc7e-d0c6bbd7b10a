-- ----------------------------------------
-- Ayafeed API 修正后的初始数据
-- 修正了字段名错误，提供了合理的富文本内容示例
-- ----------------------------------------

-- 插入示例场馆数据
INSERT INTO venues (
    id, name_en, name_ja, name_zh,
    address_en, address_ja, address_zh,
    lat, lng, website_url
) VALUES (
    "tokyo-big-sight",
    "Tokyo Big Sight",
    "東京ビッグサイト",
    "东京 Big Sight",
    "3-11-1 Ariake, Koto City, Tokyo",
    "東京都江東区有明3-11-1",
    "东京都江东区有明3-11-1",
    35.6298,
    139.793,
    "https://www.bigsight.jp/"
);

-- 插入示例展会数据
INSERT INTO events (
    id,
    name_en, name_ja, name_zh,
    date_en, date_ja, date_zh,
    date_sort, image_url,
    venue_id,
    url
) VALUES (
    "reitaisai-22",
    "Reitaisai 22",
    "第二十二回博麗神社例大祭",
    "第二十二回博丽神社例大祭",
    "May 3, 2025 (Sat) 10:30 – 15:30",
    "2025年5月3日(土・祝) 10:30 – 15:30",
    "2025年5月3日(周六) 10:30 – 15:30",
    20250503,
    "/images/events/reitaisai-22/thumb.jpg",
    "tokyo-big-sight",
    "https://reitaisai.com/rts22/"
),
(
    "comiket-105",
    "Comiket 105",
    "コミックマーケット105",
    "Comic Market 105",
    "December 30-31, 2024 (Mon-Tue) 10:00 – 16:00",
    "2024年12月30日(月)・31日(火) 10:00 – 16:00",
    "2024年12月30-31日(周一-周二) 10:00 – 16:00",
    20241230,
    "/images/events/comiket-105/thumb.jpg",
    "tokyo-big-sight",
    "https://www.comiket.co.jp/"
);

-- 插入示例社团数据
INSERT INTO circles (id, name, urls) VALUES 
("circle-team-shanghai-alice", "Team Shanghai Alice", '{"website": "https://www16.big.or.jp/~zun/", "twitter": "https://twitter.com/korindo"}'),

-- 修正：插入示例艺术家数据（使用正确的字段名：name）
INSERT INTO artists (id, name, urls) VALUES 
("artist-zun", "ZUN", '{"twitter": "https://twitter.com/korindo"}'),
("artist-alphes", "alphes", '{"twitter": "https://twitter.com/alphes_"}'),
("artist-beatmario", "beatMARIO", '{"twitter": "https://twitter.com/beatmario"}');

-- 修正：插入参展记录（移除不存在的 path 字段）
INSERT INTO appearances (id, circle_id, event_id, artist_id, booth_id) VALUES 
("app-tsa-reitaisai22", "circle-team-shanghai-alice", "reitaisai-22", "artist-zun", "A-01a"),

-- 修正：插入富文本内容示例（使用正确的字段名：content）
-- 展会相关内容
INSERT INTO rich_text_contents (id, entity_type, entity_id, content_type, content) VALUES
-- Reitaisai 22 展会内容
("rtc-reitaisai22-intro", "event", "reitaisai-22", "introduction", 
 "<h2>博麗神社例大祭について</h2><p>博麗神社例大祭は、同人サークル「博麗神社社務所」が主催する東方Projectオンリーの同人誌即売会です。年2回開催され、東方Projectファンにとって最大のイベントの一つです。</p>"),
("rtc-reitaisai22-highlights", "event", "reitaisai-22", "highlights",
 "<h3>見どころ</h3><ul><li>ZUN氏による新作体験版の頒布</li><li>人気サークルによる新刊同人誌</li><li>東方アレンジ音楽CD</li><li>オリジナルグッズ販売</li></ul>"),
("rtc-reitaisai22-guide", "event", "reitaisai-22", "guide",
 "<h3>参加ガイド</h3><p><strong>開場時間：</strong>10:30（一般入場）</p><p><strong>入場料：</strong>無料</p><p><strong>注意事項：</strong></p><ul><li>カタログの事前購入を推奨</li><li>現金での支払いが基本</li><li>大きな荷物はコインロッカーへ</li></ul>"),
("rtc-reitaisai22-notices", "event", "reitaisai-22", "notices",
 "<div class='alert alert-warning'><h4>重要なお知らせ</h4><p>新型コロナウイルス感染症対策のため、マスクの着用をお願いします。体調不良の方は参加をお控えください。</p></div>"),

-- Comiket 105 展会内容
("rtc-comiket105-intro", "event", "comiket-105", "introduction",
 "<h2>コミックマーケット105について</h2><p>コミックマーケット（コミケ）は、日本最大の同人誌即売会です。年2回開催され、様々なジャンルの同人作品が集まります。</p>"),
("rtc-comiket105-highlights", "event", "comiket-105", "highlights",
 "<h3>注目ポイント</h3><ul><li>企業ブースでの限定グッズ販売</li><li>人気作品の同人誌</li><li>コスプレエリア</li><li>新人作家の発掘</li></ul>"),
("rtc-comiket105-guide", "event", "comiket-105", "guide",
 "<h3>参加ガイド</h3><p><strong>開場時間：</strong>10:00</p><p><strong>入場料：</strong>無料（カタログ購入推奨）</p><p><strong>アクセス：</strong>ゆりかもめ「国際展示場正門前」駅下車</p>"),
("rtc-comiket105-notices", "event", "comiket-105", "notices",
 "<div class='alert alert-info'><h4>お知らせ</h4><p>冬コミは寒いので防寒対策をお忘れなく。また、年末のため交通機関の混雑が予想されます。</p></div>"),

-- 场馆相关内容
("rtc-bigsight-intro", "venue", "tokyo-big-sight", "introduction",
 "<h2>東京ビッグサイトについて</h2><p>東京国際展示場（東京ビッグサイト）は、日本最大級の展示施設です。お台場にあり、様々な展示会やイベントが開催されています。</p>"),
("rtc-bigsight-guide", "venue", "tokyo-big-sight", "guide",
 "<h3>アクセス情報</h3><p><strong>電車：</strong>ゆりかもめ「国際展示場正門前」駅直結</p><p><strong>バス：</strong>都営バス、京急バス利用可能</p><p><strong>車：</strong>首都高速湾岸線「有明」出口より約3分</p>"),
("rtc-bigsight-notices", "venue", "tokyo-big-sight", "notices",
 "<div class='alert alert-info'><h4>施設情報</h4><p>館内にはレストラン、コンビニ、ATMがあります。無料WiFiも利用可能です。</p></div>"),

-- 社团相关内容
("rtc-tsa-intro", "circle", "circle-team-shanghai-alice", "introduction",
 "<h2>Team Shanghai Aliceについて</h2><p>東方Projectの原作者ZUNによる個人サークルです。弾幕シューティングゲームシリーズで知られています。</p>"),
("rtc-tsa-highlights", "circle", "circle-team-shanghai-alice", "highlights",
 "<h3>代表作品</h3><ul><li>東方紅魔郷</li><li>東方妖々夢</li><li>東方永夜抄</li><li>東方風神録</li></ul>"),

("rtc-iosys-intro", "circle", "circle-iosys", "introduction",
 "<h2>IOSYSについて</h2><p>東方アレンジ音楽で有名な音楽サークルです。ポップで親しみやすい楽曲が特徴的です。</p>"),
("rtc-iosys-highlights", "circle", "circle-iosys", "highlights",
 "<h3>人気楽曲</h3><ul><li>魔理沙は大変なものを盗んでいきました</li><li>患部で止まってすぐ溶ける</li><li>Help me, ERINNNNNN!!</li></ul>");

-- 插入图片数据示例
INSERT INTO images (id, group_id, resource_type, resource_id, image_type, variant, file_path, width, height, format) VALUES
("img-reitaisai22-poster-thumb", "group-reitaisai22-poster", "event", "reitaisai-22", "poster", "thumb", "/images/events/reitaisai-22/poster_thumb.jpg", 200, 280, "jpeg"),
("img-reitaisai22-poster-medium", "group-reitaisai22-poster", "event", "reitaisai-22", "poster", "medium", "/images/events/reitaisai-22/poster_medium.jpg", 400, 560, "jpeg"),
("img-reitaisai22-poster-large", "group-reitaisai22-poster", "event", "reitaisai-22", "poster", "large", "/images/events/reitaisai-22/poster_large.jpg", 800, 1120, "jpeg"),
("img-comiket105-poster-thumb", "group-comiket105-poster", "event", "comiket-105", "poster", "thumb", "/images/events/comiket-105/poster_thumb.jpg", 200, 280, "jpeg"),
("img-bigsight-photo-thumb", "group-bigsight-photo", "venue", "tokyo-big-sight", "gallery", "thumb", "/images/venues/tokyo-big-sight/exterior_thumb.jpg", 200, 150, "jpeg"),
("img-tsa-logo-thumb", "group-tsa-logo", "circle", "circle-team-shanghai-alice", "logo", "thumb", "/images/circles/team-shanghai-alice/logo_thumb.png", 64, 64, "png"),
("img-iosys-logo-thumb", "group-iosys-logo", "circle", "circle-iosys", "logo", "thumb", "/images/circles/iosys/logo_thumb.png", 64, 64, "png");
