-- ----------------------------------------
-- Ayafeed API 初始数据
-- 包含基础的展会、社团、艺术家等示例数据
-- ----------------------------------------

-- 插入示例展会数据（使用 venue_id 外键）
INSERT INTO venues (
    id, name_en, name_ja, name_zh,
    address_en, address_ja, address_zh,
    lat, lng, website_url
) VALUES (
    "tokyo-big-sight",
    "Tokyo Big Sight",
    "東京ビッグサイト",
    "东京 Big Sight",
    "3-11-1 Ariake, Koto City, Tokyo",
    "東京都江東区有明3-11-1",
    "东京都江东区有明3-11-1",
    35.6298,
    139.793,
    "https://www.bigsight.jp/"
);

INSERT INTO events (
    id,
    name_en, name_ja, name_zh,
    date_en, date_ja, date_zh,
    date_sort, image_url,
    venue_id,
    url
) VALUES (
    "reitaisai-22",
    "Reitaisai 22",
    "第二十二回博麗神社例大祭",
    "第二十二回博丽神社例大祭",
    "May 3, 2025 (Sat) 10:30 – 15:30",
    "2025年5月3日(土・祝) 10:30 – 15:30",
    "2025年5月3日(周六) 10:30 – 15:30",
    20250503,
    "/images/events/reitaisai-22/thumb.jpg",
    "tokyo-big-sight",
    "https://reitaisai.com/rts22/"
),
(
    "comiket-105",
    "Comiket 105",
    "コミックマーケット105",
    "Comic Market 105",
    "December 30-31, 2024 (Mon-Tue) 10:00 – 16:00",
    "2024年12月30日(月)・31日(火) 10:00 – 16:00",
    "2024年12月30-31日(周一-周二) 10:00 – 16:00",
    20241230,
    "/images/events/comiket-105/thumb.jpg",
    "tokyo-big-sight",
    "https://www.comiket.co.jp/"
);

-- 插入示例社团数据
INSERT INTO circles (id, name, urls) VALUES 
("circle-team-shanghai-alice", "Team Shanghai Alice", '{"website": "https://www16.big.or.jp/~zun/", "twitter": "https://twitter.com/korindo"}'),
("circle-twilight-frontier", "Twilight Frontier", '{"website": "https://www.tasofro.net/"}'),
("circle-iosys", "IOSYS", '{"website": "http://iosys.co.jp/", "twitter": "https://twitter.com/iosysos"}'),
("circle-sound-holic", "SoundHolic", '{"website": "http://www.sound-holic.com/"}');

-- 插入示例艺术家数据（使用多语言字段）
INSERT INTO artists (id, name_en, name_ja, name_zh, urls) VALUES 
("artist-zun", "ZUN", "ZUN", "ZUN", '{"twitter": "https://twitter.com/korindo"}'),
("artist-alphes", "alphes", "alphes", "alphes", '{"twitter": "https://twitter.com/alphes_"}'),
("artist-beatmario", "beatMARIO", "beatMARIO", "beatMARIO", '{"twitter": "https://twitter.com/beatmario"}');

-- 插入参展记录（使用正确的字段名）
INSERT INTO appearances (id, circle_id, event_id, artist_id, booth_id, path) VALUES 
("app-tsa-reitaisai22", "circle-team-shanghai-alice", "reitaisai-22", "artist-zun", "A-01a", "/2025/05/03/A01a.jpg"),
("app-tf-reitaisai22", "circle-twilight-frontier", "reitaisai-22", "artist-alphes", "A-02a", "/2025/05/03/A02a.jpg"),
("app-iosys-comiket105", "circle-iosys", "comiket-105", "artist-beatmario", "東A-01a", "/2024/12/30/東A01a.jpg"),
("app-sh-comiket105", "circle-sound-holic", "comiket-105", NULL, "東A-15b", "/2024/12/30/東A15b.jpg");

-- 插入富文本内容示例
INSERT INTO rich_text_contents (id, entity_type, entity_id, content_type, content_json, content_html) VALUES
("rtc-reitaisai22-desc", "event", "reitaisai-22", "description", 
 '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"東方Project最大の同人誌即売会"}]}]}',
 "<p>東方Project最大の同人誌即売会</p>"),
("rtc-tsa-desc", "circle", "circle-team-shanghai-alice", "description",
 '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"東方Projectの原作者ZUNによる個人サークル"}]}]}',
 "<p>東方Projectの原作者ZUNによる個人サークル</p>");

-- 插入图片数据示例
INSERT INTO images (id, group_id, resource_type, resource_id, image_type, variant, file_path, width, height, format) VALUES
("img-reitaisai22-poster-thumb", "group-reitaisai22-poster", "event", "reitaisai-22", "poster", "thumb", "/images/events/reitaisai-22/poster_thumb.jpg", 200, 280, "jpeg"),
("img-reitaisai22-poster-medium", "group-reitaisai22-poster", "event", "reitaisai-22", "poster", "medium", "/images/events/reitaisai-22/poster_medium.jpg", 400, 560, "jpeg"),
("img-reitaisai22-poster-large", "group-reitaisai22-poster", "event", "reitaisai-22", "poster", "large", "/images/events/reitaisai-22/poster_large.jpg", 800, 1120, "jpeg"),
("img-tsa-logo-thumb", "group-tsa-logo", "circle", "circle-team-shanghai-alice", "logo", "thumb", "/images/circles/team-shanghai-alice/logo_thumb.png", 64, 64, "png");
