# Better Auth 迁移指南

> 从自研用户系统迁移到 Better Auth 的完整指南

## 📋 迁移概述

### 迁移目标

- ✅ 解决邮箱登录功能缺失问题
- ✅ 实现找回密码功能
- ✅ 获得完整的认证功能生态
- ✅ 减少长期维护成本

### 技术栈兼容性

- **Hono 框架** ✅ Better Auth 原生支持
- **Cloudflare D1** ✅ Better Auth 官方支持
- **Drizzle ORM** ✅ Better Auth 官方适配器
- **TypeScript** ✅ Better Auth 原生支持

## 🚀 迁移计划

### 阶段一：准备工作（3-5天）

#### 1.1 环境准备

```bash
# 安装 Better Auth 核心包
pnpm add better-auth

# 安装 Drizzle 适配器
pnpm add better-auth/adapters/drizzle

# 安装 CLI 工具
pnpm add -D @better-auth/cli
```

#### 1.2 邮件服务配置

选择邮件服务提供商（推荐 Resend）：

```bash
pnpm add resend
```

#### 1.3 数据备份

```sql
-- 备份现有用户数据
.backup backup_$(date +%Y%m%d_%H%M%S).db

-- 导出用户数据
SELECT * FROM auth_user;
SELECT * FROM auth_session;
SELECT * FROM auth_key;
```

### 阶段二：Better Auth 配置（2-3天）

#### 2.1 生成数据库 Schema

```bash
# 生成 Better Auth 数据库结构
npx @better-auth/cli generate
```

#### 2.2 创建 Better Auth 配置

创建 `src/lib/auth.ts`：

```typescript
import { betterAuth } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { db } from '@/infrastructure/db';

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: 'sqlite', // Cloudflare D1
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
  },
  emailVerification: {
    enabled: true,
    expiresIn: 60 * 60 * 24, // 24小时
  },
  session: {
    expiresIn: 60 * 60 * 24 * 30, // 30天
    updateAge: 60 * 60 * 24, // 1天
  },
  // 邮件配置
  emailProvider: {
    type: 'resend',
    apiKey: process.env.RESEND_API_KEY!,
    from: '<EMAIL>',
  },
  // 保留现有角色系统
  databaseHooks: {
    user: {
      create: {
        before: async (user) => {
          return {
            data: {
              ...user,
              role: user.role || 'viewer', // 默认角色
            },
          };
        },
      },
    },
  },
});

export type Auth = typeof auth;
```

#### 2.3 环境变量配置

更新 `.env`：

```env
# Better Auth 配置
BETTER_AUTH_SECRET="your-secret-key-here"
BETTER_AUTH_URL="http://localhost:8787"

# 邮件服务配置
RESEND_API_KEY="your-resend-api-key"

# 现有配置保持不变
DATABASE_URL="your-d1-database-url"
```

### 阶段三：数据迁移（3-5天）

#### 3.1 创建迁移脚本

创建 `scripts/migrate-to-better-auth.ts`：

```typescript
import { auth } from '@/lib/auth';
import { db } from '@/infrastructure/db';

interface LegacyUser {
  id: string;
  username: string;
  role: string;
  created_at: string;
  updated_at: string;
}

interface LegacyAuthKey {
  id: string;
  user_id: string;
  hashed_password: string;
}

async function migrateUsers() {
  console.log('开始迁移用户数据...');

  // 1. 获取现有用户数据
  const legacyUsers = (await db
    .prepare(
      `
    SELECT id, username, role, created_at, updated_at 
    FROM auth_user
  `
    )
    .all()) as LegacyUser[];

  const legacyKeys = (await db
    .prepare(
      `
    SELECT id, user_id, hashed_password 
    FROM auth_key
  `
    )
    .all()) as LegacyAuthKey[];

  // 2. 创建用户名到密码的映射
  const passwordMap = new Map();
  legacyKeys.forEach((key) => {
    passwordMap.set(key.user_id, key.hashed_password);
  });

  // 3. 迁移到 Better Auth
  for (const user of legacyUsers) {
    try {
      // 创建 Better Auth 用户
      await db
        .prepare(
          `
        INSERT INTO user (id, name, email, emailVerified, createdAt, updatedAt, role)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `
        )
        .run(
          user.id,
          user.username,
          `${user.username}@temp.local`, // 临时邮箱，后续用户可以更新
          false,
          user.created_at,
          user.updated_at,
          user.role
        );

      // 创建密码记录
      const hashedPassword = passwordMap.get(user.id);
      if (hashedPassword) {
        await db
          .prepare(
            `
          INSERT INTO account (id, accountId, providerId, userId, password, createdAt, updatedAt)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `
          )
          .run(
            `${user.id}_password`,
            user.username,
            'credential',
            user.id,
            hashedPassword,
            user.created_at,
            user.updated_at
          );
      }

      console.log(`✅ 迁移用户: ${user.username}`);
    } catch (error) {
      console.error(`❌ 迁移失败: ${user.username}`, error);
    }
  }

  console.log('用户数据迁移完成！');
}

// 执行迁移
migrateUsers().catch(console.error);
```

#### 3.2 执行迁移

```bash
# 运行迁移脚本
tsx scripts/migrate-to-better-auth.ts
```

### 阶段四：更新应用代码（5-7天）

#### 4.1 更新 Hono 路由

更新 `src/index.ts`：

```typescript
import { Hono } from 'hono';
import { auth } from '@/lib/auth';

const app = new Hono();

// Better Auth 路由处理
app.on(['POST', 'GET'], '/api/auth/*', (c) => {
  return auth.handler(c.req.raw);
});

// 添加认证中间件
app.use('*', async (c, next) => {
  const session = await auth.api.getSession({
    headers: c.req.raw.headers,
  });

  if (session) {
    c.set('user', session.user);
    c.set('session', session.session);
  } else {
    c.set('user', null);
    c.set('session', null);
  }

  return next();
});

export default app;
```

#### 4.2 创建前端客户端

创建 `src/lib/auth-client.ts`：

```typescript
import { createAuthClient } from 'better-auth/client';

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
});

// 导出常用方法
export const {
  signIn,
  signUp,
  signOut,
  resetPassword,
  verifyEmail,
  useSession,
} = authClient;
```

#### 4.3 更新认证组件

更新现有的登录组件：

```typescript
import { authClient } from '@/lib/auth-client';

export function LoginForm() {
  const handleLogin = async (email: string, password: string) => {
    try {
      await authClient.signIn.email({
        email,
        password,
      });
      // 登录成功处理
    } catch (error) {
      // 错误处理
    }
  };

  // 组件 JSX...
}
```

### 阶段五：功能补齐（3-5天）

#### 5.1 角色权限系统适配

创建 `src/lib/permissions.ts`：

```typescript
import type { User } from 'better-auth';

export function hasPermission(user: User | null, permission: string): boolean {
  if (!user) return false;

  const role = (user as any).role;

  switch (role) {
    case 'admin':
      return true;
    case 'editor':
      return ['read', 'write', 'edit'].includes(permission);
    case 'viewer':
      return ['read'].includes(permission);
    default:
      return false;
  }
}
```

#### 5.2 用户活动日志集成

更新现有的日志系统以支持 Better Auth：

```typescript
// 在 Better Auth hooks 中记录活动
export const auth = betterAuth({
  // ... 其他配置
  databaseHooks: {
    user: {
      create: {
        after: async (user) => {
          await recordActivity({
            userId: user.id,
            action: 'USER_REGISTERED',
            timestamp: new Date(),
          });
        },
      },
    },
    session: {
      create: {
        after: async (session) => {
          await recordActivity({
            userId: session.userId,
            action: 'USER_LOGIN',
            timestamp: new Date(),
          });
        },
      },
    },
  },
});
```

### 阶段六：测试验证（3-5天）

#### 6.1 功能测试清单

- [ ] 邮箱注册功能
- [ ] 邮箱登录功能
- [ ] 邮箱验证功能
- [ ] 找回密码功能
- [ ] 会话管理功能
- [ ] 角色权限功能
- [ ] 用户活动日志
- [ ] 现有用户数据完整性

#### 6.2 测试脚本

创建 `tests/auth-migration.test.ts`：

```typescript
import { describe, it, expect } from 'vitest';
import { authClient } from '@/lib/auth-client';

describe('Better Auth Migration', () => {
  it('should allow email login', async () => {
    // 测试邮箱登录
  });

  it('should support password reset', async () => {
    // 测试密码重置
  });

  it('should preserve user roles', async () => {
    // 测试角色保持
  });
});
```

## 🔧 故障排除

### 常见问题

#### 1. 数据迁移失败

```bash
# 检查数据库连接
npx @better-auth/cli migrate --dry-run

# 手动检查数据完整性
SELECT COUNT(*) FROM user;
SELECT COUNT(*) FROM account;
```

#### 2. 邮件发送失败

```typescript
// 检查邮件配置
console.log(
  'RESEND_API_KEY:',
  process.env.RESEND_API_KEY ? '已设置' : '未设置'
);
```

#### 3. 会话问题

```typescript
// 检查 cookie 配置
export const auth = betterAuth({
  advanced: {
    defaultCookieAttributes: {
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
    },
  },
});
```

## 📚 参考资源

- [Better Auth 官方文档](https://better-auth.com/docs)
- [Hono 集成指南](https://better-auth.com/docs/integrations/hono)
- [Drizzle 适配器文档](https://better-auth.com/docs/adapters/drizzle)
- [Cloudflare D1 配置](https://better-auth.com/docs/adapters/other-relational-databases)

## 🎯 迁移后验证

### 成功标准

- ✅ 所有现有用户可以正常登录
- ✅ 邮箱登录功能正常工作
- ✅ 找回密码功能正常工作
- ✅ 角色权限系统正常工作
- ✅ 用户活动日志正常记录

### 性能指标

- 登录响应时间 < 500ms
- 邮件发送成功率 > 99%
- 数据迁移完整性 100%

## 🔄 回滚方案

### 紧急回滚步骤

如果迁移过程中出现严重问题，可以快速回滚：

```bash
# 1. 停止应用服务
pm2 stop ayafeed-api

# 2. 恢复数据库备份
cp backup_YYYYMMDD_HHMMSS.db current.db

# 3. 切换到备份分支
git checkout backup-before-migration

# 4. 重启服务
pm2 start ayafeed-api
```

### 数据一致性检查

```sql
-- 验证用户数据完整性
SELECT
  (SELECT COUNT(*) FROM auth_user) as legacy_users,
  (SELECT COUNT(*) FROM user) as better_auth_users;

-- 检查密码数据
SELECT
  (SELECT COUNT(*) FROM auth_key) as legacy_passwords,
  (SELECT COUNT(*) FROM account WHERE providerId = 'credential') as better_auth_passwords;
```

## 📞 支持与帮助

### 迁移支持

- **技术支持**：Better Auth Discord 社区
- **文档参考**：https://better-auth.com/docs
- **问题反馈**：GitHub Issues

### 应急联系

- 如遇到紧急问题，立即执行回滚方案
- 保留详细的错误日志用于后续分析
- 记录迁移过程中的所有变更

## 📋 迁移检查表

### 迁移前准备

- [ ] 完整备份现有数据库
- [ ] 准备测试环境
- [ ] 配置邮件服务
- [ ] 团队成员培训

### 迁移执行

- [ ] 安装 Better Auth 依赖
- [ ] 配置认证系统
- [ ] 执行数据迁移
- [ ] 更新应用代码
- [ ] 功能测试验证

### 迁移后验证

- [ ] 用户登录测试
- [ ] 邮箱功能测试
- [ ] 权限系统测试
- [ ] 性能监控
- [ ] 用户反馈收集

## 🎉 迁移完成后的优势

### 立即获得的功能

- ✅ **邮箱登录** - 解决当前痛点
- ✅ **找回密码** - 完整的密码重置流程
- ✅ **邮箱验证** - 提升账户安全性
- ✅ **会话管理** - 更安全的会话处理

### 未来可扩展功能

- 🚀 **社交登录** - GitHub、Google 等
- 🔐 **双因素认证** - 提升安全等级
- 🏢 **多租户支持** - 组织和团队管理
- 🔌 **插件生态** - 丰富的扩展功能

### 维护优势

- 📉 **减少维护负担** - 社区维护核心认证逻辑
- 🛡️ **安全更新** - 及时的安全补丁和更新
- 📚 **完善文档** - 详细的使用文档和示例
- 🤝 **社区支持** - 活跃的开发者社区

---

**预计总迁移时间：2-3 周**
**建议团队规模：1-2 名开发者**
**风险等级：中等（有完整回滚方案）**
**投资回报：高（立即解决痛点 + 长期维护优势）**

> 💡 **建议**：建议在低峰期执行迁移，并提前通知用户可能的短暂服务中断。迁移完成后，用户将获得更好的认证体验和更多功能选择。
