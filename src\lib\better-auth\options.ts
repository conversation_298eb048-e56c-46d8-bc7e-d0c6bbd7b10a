import { BetterAuthOptions } from 'better-auth';

/**
 * Better Auth 配置选项 - 适配 Cloudflare D1
 */
export const betterAuthOptions: BetterAuthOptions = {
  /**
   * 应用名称
   */
  appName: 'AyaFeed',

  /**
   * Better Auth API 基础路径
   */
  basePath: '/api/auth',

  /**
   * 启用邮箱密码登录
   */
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // D1 环境下先禁用邮件验证
    minPasswordLength: 6,
    maxPasswordLength: 128,
  },

  session: {
    expiresIn: 60 * 60 * 24 * 30, // 30天
    updateAge: 60 * 60 * 24, // 1天更新一次
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5, // 5分钟
    },
  },

  /**
   * 用户配置 - 根据官方文档格式
   */
  user: {
    modelName: 'user', // 数据库表名
    fields: {
      email: 'email',
      name: 'username',
    },
    additionalFields: {
      role: {
        type: 'string',
        required: true,
        defaultValue: 'user',
      },
    },
  },

  /**
   * 高级配置
   */
  advanced: {
    crossSubDomainCookies: {
      enabled: false,
    },
    defaultCookieAttributes: {
      sameSite: 'lax',
      httpOnly: true,
      secure: false, // 开发环境设为 false
      path: '/',
    },
    generateId: false,
  },

  rateLimit: {
    window: 60,
    max: 100,
    storage: 'memory',
  },

  trustedOrigins: ['http://localhost:3000', 'https://ayafeed.com'],
  plugins: [],
};
